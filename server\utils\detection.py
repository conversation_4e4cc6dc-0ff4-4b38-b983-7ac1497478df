from ultralytics import YOLO
import cv2
import numpy as np
import torch

class Detector:
    def __init__(self, model_path):
        # 检查CUDA可用性
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device: {self.device}")

        # 加载模型 - 优化配置
        self.model = YOLO(model_path)

        # 设置模型为评估模式以提高推理速度
        self.model.model.eval()

        # 如果使用CUDA，启用优化
        if self.device == 'cuda':
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

        self.last_counts = {}
        self.class_names = self.model.names

        # 预热模型 - 使用更小的图像进行预热
        dummy_frame = np.zeros((320, 320, 3), dtype=np.uint8)
        try:
            _ = self.model(dummy_frame, verbose=False, device=self.device)
            print("Model warmed up successfully")
        except Exception as e:
            print(f"Model warmup failed: {e}, but continuing...")

    def detect(self, frame):
        # 优化检测 - 降低输入分辨率以提高速度
        height, width = frame.shape[:2]

        # 如果图像过大，先缩放到合适大小
        if width > 640 or height > 640:
            scale = min(640/width, 640/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame_resized = cv2.resize(frame, (new_width, new_height))
        else:
            frame_resized = frame
            scale = 1.0

        # 运行YOLOv8检测 - 使用优化参数
        results = self.model(
            frame_resized,
            verbose=False,
            device=self.device,
            conf=0.5,  # 降低置信度阈值以减少计算
            iou=0.7,   # 设置IoU阈值
            max_det=100  # 限制最大检测数量
        )

        # 解析结果
        boxes = results[0].boxes
        self.last_counts = {}

        # 绘制检测结果到原始尺寸
        if scale != 1.0:
            # 将检测结果缩放回原始尺寸
            annotated_frame = results[0].plot()
            annotated_frame = cv2.resize(annotated_frame, (width, height))
        else:
            annotated_frame = results[0].plot()

        # 统计各类别数量
        if boxes is not None and len(boxes) > 0:
            for class_id in boxes.cls.unique():
                class_name = self.class_names[int(class_id)]
                count = len(boxes[boxes.cls == class_id])
                self.last_counts[class_name] = count

        return annotated_frame, self.last_counts

    def get_last_counts(self):
        return self.last_counts
