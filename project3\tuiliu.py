import subprocess

# 优化后的 FFmpeg 命令
ffmpeg_cmd = [
    "ffmpeg",

    # 输入设置
    "-f", "dshow",
    "-video_size", "640x480",  # 降低分辨率可进一步减少延迟
    "-framerate", "30",  # 适当降低帧率
    "-i", "video=Q8 HD Webcam",  # 摄像头设备名

    # 视频处理
    "-vf", "scale=640:480,format=yuv420p,"  # 保持原始比例640x480
           "drawtext=text='%{localtime\\:%T}':fontsize=32:box=1:boxcolor=black@0.5:x=10:y=10",

    # 编码设置 (关键优化部分)
    "-c:v", "libx264",
    "-preset", "ultrafast",  # 最快的编码预设
    "-tune", "zerolatency",  # 零延迟优化
    "-x264-params", "keyint=30:min-keyint=30:no-scenecut=1:sync-lookahead=0:rc-lookahead=0",
    "-b:v", "1000k",  # 适当提高比特率
    "-maxrate", "1000k",
    "-bufsize", "1000k",  # 小缓冲区减少延迟
    "-g", "30",  # GOP大小(关键帧间隔)
    "-pix_fmt", "yuv420p",

    # 传输优化
    "-f", "flv",
    "-flvflags", "no_duration_filesize",  # FLV特定优化
    "-movflags", "faststart",

    # 低延迟参数
    "-threads", "4",  # 多线程处理
    "-fflags", "nobuffer",  # 减少输入缓冲
    "-flags", "low_delay",  # 低延迟标志
    "-strict", "experimental",  # 允许实验性参数

    "rtmp://127.0.0.1/live/stream1"
]

# 启动 FFmpeg 进程
process = subprocess.Popen(
    ffmpeg_cmd,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    universal_newlines=True,
    shell=True
)

# 实时监控和错误处理
try:
    while True:
        line = process.stderr.readline()
        if line:
            # 过滤并显示重要日志
            if "frame=" in line or "speed=" in line:
                print(line.strip())
        if process.poll() is not None:
            break
except KeyboardInterrupt:
    print("\n用户终止推流...")
finally:
    if process.poll() is None:
        process.terminate()
    print("推流已停止")