import os

# 模型路径 - 使用绝对路径
MODEL_PATH = os.path.join(os.path.dirname(__file__), 'models', 'best.pt')

# RTMP配置 - 采用project3的低延迟配置
RTMP_SERVER = 'rtmp://localhost/live'
RTMP_STREAM = 'stream'
RTMP_URL = f"{RTMP_SERVER}/{RTMP_STREAM}"

# 摄像头默认配置 - 优化为低延迟
DEFAULT_CAMERA_INDEX = 0
DEFAULT_RESOLUTION = '640x480'  # 降低分辨率以减少延迟
DEFAULT_FPS = 30

# 低延迟优化配置
LOW_LATENCY_MODE = True
DETECTION_CONFIDENCE = 0.5  # 降低置信度以提高速度
MAX_DETECTIONS = 100  # 限制最大检测数量

# 其他配置
DEBUG = True
