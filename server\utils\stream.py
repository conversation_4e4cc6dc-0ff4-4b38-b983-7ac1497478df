import subprocess
import cv2


class StreamManager:
    """
    RTMP流管理器，用于管理多个不同分辨率和帧率的视频流

    属性:
        rtmp_url (str): RTMP服务器地址
        processes (dict): 存储不同配置的流处理器
    """

    def __init__(self, rtmp_url):
        """
        初始化流管理器

        参数:
            rtmp_url: RTMP服务器地址 (如: rtmp://localhost/live/stream)
        """
        self.rtmp_url = rtmp_url
        self.processes = {}  # 存储不同分辨率/帧率的流处理器

    def get_streamer(self, resolution='640x640', fps=30):
        """
        获取指定配置的视频流处理器

        参数:
            resolution: 视频分辨率 (格式: '宽x高')
            fps: 帧率 (frames per second)

        返回:
            Streamer: 配置好的流处理器对象
        """
        # 生成配置键
        key = f"{resolution}_{fps}"

        # 如果已有相同配置的处理器，直接返回
        if key in self.processes:
            return self.processes[key]

        # 解析分辨率 (如: '640x480' -> (640, 480))
        width, height = map(int, resolution.split('x'))

        # 构建FFmpeg推流命令 - 采用project3的超低延时优化
        command = [
            'ffmpeg',
            '-y',  # 覆盖输出文件而不询问

            # 输入设置
            '-f', 'rawvideo',  # 输入格式为原始视频
            '-vcodec', 'rawvideo',  # 原始视频编解码器
            '-pix_fmt', 'bgr24',  # OpenCV默认的像素格式
            '-s', f"{width}x{height}",  # 视频分辨率
            '-r', str(fps),  # 帧率
            '-i', '-',  # 从标准输入读取数据

            # 视频编码优化 - project3配置
            '-c:v', 'libx264',  # 使用H.264编码
            '-preset', 'ultrafast',  # 最快编码预设
            '-tune', 'zerolatency',  # 零延迟调优
            '-x264-params', 'keyint=30:min-keyint=30:no-scenecut=1:sync-lookahead=0:rc-lookahead=0',
            '-b:v', '1000k',  # 视频码率
            '-maxrate', '1000k',  # 最大码率
            '-bufsize', '1000k',  # 缓冲区大小
            '-g', '30',  # GOP大小

            # 传输优化
            '-f', 'flv',  # 输出格式为FLV
            '-flvflags', 'no_duration_filesize',  # FLV特定优化
            '-movflags', 'faststart',

            # 低延迟参数 - project3配置
            '-threads', '4',  # 多线程处理
            '-fflags', 'nobuffer',  # 减少输入缓冲
            '-flags', 'low_delay',  # 低延迟标志
            '-strict', 'experimental',  # 允许实验性参数

            # 输出格式
            '-pix_fmt', 'yuv420p',  # 输出像素格式

            self.rtmp_url  # RTMP服务器地址
        ]

        # 启动FFmpeg子进程
        process = subprocess.Popen(
            command,
            stdin=subprocess.PIPE,  # 标准输入管道(用于发送视频帧)
            stdout=subprocess.PIPE,  # 标准输出管道
            stderr=subprocess.PIPE  # 标准错误管道
        )

        # 创建流处理器对象
        streamer = Streamer(process, width, height)

        # 缓存处理器
        self.processes[key] = streamer

        return streamer

    def release_all(self):
        """
        释放所有流处理器资源
        """
        for streamer in self.processes.values():
            streamer.release()
        self.processes.clear()  # 清空处理器字典


class Streamer:
    """
    视频流处理器，负责处理视频帧并推送到RTMP服务器

    属性:
        process: FFmpeg子进程对象
        width: 视频宽度
        height: 视频高度
    """

    def __init__(self, process, width, height):
        """
        初始化流处理器

        参数:
            process: FFmpeg子进程
            width: 视频宽度
            height: 视频高度
        """
        self.process = process
        self.width = width
        self.height = height

    def process_frame(self, frame):
        """
        处理视频帧并推送到流

        参数:
            frame: OpenCV视频帧 (numpy数组)
        """
        # 调整帧大小以匹配流分辨率
        if frame.shape[0] != self.height or frame.shape[1] != self.width:
            frame = cv2.resize(frame, (self.width, self.height))

        # 将帧数据写入FFmpeg的标准输入
        # tobytes()将numpy数组转换为字节序列
        self.process.stdin.write(frame.tobytes())

    def release(self):
        """
        释放流处理器资源
        """
        try:
            # 关闭标准输入管道
            self.process.stdin.close()
            # 终止进程
            self.process.terminate()
            # 等待进程结束(最多5秒)
            self.process.wait(timeout=5)
        except Exception as e:
            # 忽略任何异常
            pass
