2025-06-09 20:46:22,378 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:46:22,378 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:46:22,378 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:46:22,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:46:22,385 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:07,680 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:53:07,680 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:53:07,686 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:53:07,690 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:53:07,690 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:19,167 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:19,181 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/droid-sans.css HTTP/1.1" 200 -
2025-06-09 20:53:19,397 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui.css HTTP/1.1" 200 -
2025-06-09 20:53:19,477 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-bundle.js HTTP/1.1" 200 -
2025-06-09 20:53:19,478 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1" 200 -
2025-06-09 20:53:19,557 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:19,888 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/favicon-32x32.png HTTP/1.1" 200 -
2025-06-09 20:53:55,852 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:55,884 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "[36mGET /swaggerui/droid-sans.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,108 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,172 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-bundle.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,173 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,243 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:56,570 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/favicon-32x32.png HTTP/1.1[0m" 304 -
2025-06-09 20:55:01,668 - INFO - Received status request
2025-06-09 20:55:01,668 - INFO - Current status: stopped, counts: {}
2025-06-09 20:55:01,669 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:01] "GET /api/status HTTP/1.1" 200 -
2025-06-09 20:55:03,707 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 20:55:03,707 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 20:55:03,708 - INFO - StreamThread-20250609205503 - Starting stream processing
2025-06-09 20:55:03,708 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:55:03,709 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:03] "POST /api/start HTTP/1.1" 200 -
2025-06-09 20:55:05,224 - INFO - StreamThread-20250609205503 - Camera initialized
2025-06-09 20:55:05,225 - INFO - StreamThread-20250609205503 - Streamer initialized
2025-06-09 20:55:12,239 - INFO - StreamThread-20250609205503 - Processed 100 frames, FPS: 14.26
2025-06-09 20:55:17,598 - INFO - StreamThread-20250609205503 - Processed 200 frames, FPS: 18.66
2025-06-09 20:55:22,878 - INFO - StreamThread-20250609205503 - Processed 300 frames, FPS: 18.94
2025-06-09 20:55:28,175 - INFO - StreamThread-20250609205503 - Processed 400 frames, FPS: 18.88
2025-06-09 20:55:33,455 - INFO - StreamThread-20250609205503 - Processed 500 frames, FPS: 18.94
2025-06-09 20:55:38,750 - INFO - StreamThread-20250609205503 - Processed 600 frames, FPS: 18.88
2025-06-09 20:55:44,047 - INFO - StreamThread-20250609205503 - Processed 700 frames, FPS: 18.88
2025-06-09 20:55:49,327 - INFO - StreamThread-20250609205503 - Processed 800 frames, FPS: 18.94
2025-06-10 18:09:43,704 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:09:43,704 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:09:43,704 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:09:43,710 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:09:43,710 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:10:14,968 - INFO - Received status request
2025-06-10 18:10:14,969 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:14,970 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:16,955 - INFO - Received status request
2025-06-10 18:10:16,956 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:16,956 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:18,958 - INFO - Received status request
2025-06-10 18:10:18,958 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:18,959 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:20,936 - INFO - Received status request
2025-06-10 18:10:20,936 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:20,937 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:22,942 - INFO - Received status request
2025-06-10 18:10:22,942 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:22,942 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:24,952 - INFO - Received status request
2025-06-10 18:10:24,953 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:24,953 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:26,999 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:10:26,999 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:10:27,000 - INFO - StreamThread-20250610181026 - Starting stream processing with low-latency optimization
2025-06-10 18:10:27,000 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:10:27,002 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:27] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:10:29,180 - INFO - StreamThread-20250610181026 - Camera initialized
2025-06-10 18:10:29,183 - INFO - StreamThread-20250610181026 - Streamer initialized
2025-06-10 18:10:30,185 - ERROR - StreamThread-20250610181026 - Failed to read frame from camera
2025-06-10 18:10:30,190 - INFO - StreamThread-20250610181026 - Resources released, processed 1 frames in total
2025-06-10 18:11:18,174 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:11:18,174 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:11:18,174 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:11:18,179 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:11:18,180 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,863 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:12:24,863 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,865 - INFO -  * Restarting with stat
