from flask import Flask, request
from flask_restx import Api, Resource, fields
from utils.camera import CameraManager
from utils.stream import StreamManager
from utils.detection import Detector
import threading
import config
import logging
import time
from datetime import datetime

app = Flask(__name__)
api = Api(app, version='1.0', title='Stream API', description='视频流处理API')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger(__name__)

# 初始化组件
camera_manager = CameraManager()
stream_manager = StreamManager(config.RTMP_URL)
detector = Detector(config.MODEL_PATH)

# 全局状态
processing = False
stream_thread = None

# 数据模型定义
start_model = api.model('StartStream', {
    'camera_index': fields.Integer(default=0, description='摄像头索引'),
    'resolution': fields.String(default='640x640', description='分辨率'),
    'fps': fields.Integer(default=30, description='帧率'),
    'confidence_threshold': fields.Float(default=0.7, description='置信度阈值'),
    'classes': fields.List(fields.Integer, description='检测类别')
})

status_model = api.model('Status', {
    'status': fields.String(description='运行状态'),
    'counts': fields.Raw(description='检测计数'),
    'message': fields.String(description='附加信息'),
    'rtmp_url': fields.String(description='RTMP流地址')
})


@api.route('/api/start')
class StartStream(Resource):
    @api.expect(start_model)
    @api.marshal_with(status_model)
    def post(self):
        """启动视频流处理"""
        global processing, stream_thread

        logger.info(f"Received start request with data: {request.json}")

        if processing:
            logger.warning("Start request rejected: stream already running")
            return {"status": "error", "message": "Stream is already running"}, 400

        # 获取参数
        data = request.json
        camera_index = data.get('camera_index', 0)
        resolution = data.get('resolution', '640x640')
        fps = data.get('fps', 30)

        logger.info(f"Starting stream with params - camera: {camera_index}, resolution: {resolution}, fps: {fps}")

        # 启动处理线程
        processing = True
        stream_thread = threading.Thread(
            target=process_stream,
            args=(camera_index, resolution, fps),
            name=f"StreamThread-{datetime.now().strftime('%Y%m%d%H%M%S')}")
        stream_thread.start()

        logger.info(f"Stream started successfully, RTMP URL: {config.RTMP_URL}")
        return {
            "status": "success",
            "rtmp_url": config.RTMP_URL,
            "message": "Stream started"
        }


@api.route('/api/stop')
class StopStream(Resource):
    @api.marshal_with(status_model)
    def get(self):
        """停止视频流处理"""
        global processing, stream_thread

        logger.info("Received stop request")

        if not processing:
            logger.warning("Stop request rejected: no stream running")
            return {"status": "error", "message": "No stream is running"}, 400

        logger.info("Stopping stream...")
        processing = False
        if stream_thread:
            stream_thread.join()
            logger.info("Stream thread joined successfully")

        logger.info("Stream stopped successfully")
        return {"status": "success", "message": "Stream stopped"}


@api.route('/api/status')
class StreamStatus(Resource):
    @api.marshal_with(status_model)
    def get(self):
        """获取当前状态"""
        logger.info("Received status request")
        counts = detector.get_last_counts()
        status = "running" if processing else "stopped"
        logger.info(f"Current status: {status}, counts: {counts}")
        return {
            "status": status,
            "counts": counts
        }


def process_stream(camera_index, resolution, fps):
    """视频流处理线程函数 - 采用project3的低延迟优化"""
    thread_name = threading.current_thread().name
    logger.info(f"{thread_name} - Starting stream processing with low-latency optimization")

    # 初始化摄像头
    camera = camera_manager.get_camera(camera_index, resolution, fps)
    logger.info(f"{thread_name} - Camera initialized")

    # 初始化FFmpeg推流
    streamer = stream_manager.get_streamer(resolution, fps)
    logger.info(f"{thread_name} - Streamer initialized")

    frame_count = 0
    last_time = time.time()
    skip_detection_counter = 0  # 跳帧检测计数器

    try:
        while processing:
            # 读取帧
            ret, frame = camera.read()
            if not ret:
                logger.error(f"{thread_name} - Failed to read frame from camera")
                break

            frame_count += 1

            # 每100帧输出一次状态
            if frame_count % 100 == 0:
                current_time = time.time()
                fps_actual = 100 / (current_time - last_time)
                logger.info(f"{thread_name} - Processed {frame_count} frames, FPS: {fps_actual:.2f}")
                last_time = current_time

            try:
                # 低延迟优化：每3帧进行一次检测，其他帧直接推流
                if skip_detection_counter % 3 == 0:
                    # 检测电子元器件
                    detected_frame, _ = detector.detect(frame)
                    last_detected_frame = detected_frame
                else:
                    # 跳过检测，使用上一次的检测结果或原始帧
                    if 'last_detected_frame' in locals():
                        detected_frame = last_detected_frame
                    else:
                        detected_frame = frame

                skip_detection_counter += 1

                # 推流
                streamer.process_frame(detected_frame)

            except Exception as e:
                logger.warning(f"{thread_name} - Frame processing error: {str(e)}")
                # 出错时直接推流原始帧
                try:
                    streamer.process_frame(frame)
                except:
                    pass

    except Exception as e:
        logger.error(f"{thread_name} - Error in stream processing: {str(e)}", exc_info=True)
    finally:
        camera.release()
        streamer.release()
        logger.info(f"{thread_name} - Resources released, processed {frame_count} frames in total")


if __name__ == '__main__':
    logger.info("Starting YOLOv8 detection system with low-latency optimization...")
    logger.info(f"RTMP URL: {config.RTMP_URL}")
    logger.info(f"Model: {config.MODEL_PATH}")

    app.run(host='0.0.0.0', port=5000, threaded=True)
