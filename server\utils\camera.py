import cv2

class CameraManager:
    def __init__(self):
        self.cameras = {}

    def get_camera(self, index, resolution='640x640', fps=30):
        if index in self.cameras:
            return self.cameras[index]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))

        # 打开摄像头 - 采用project3的超低延迟配置
        cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)

        # 设置参数
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        cap.set(cv2.CAP_PROP_FPS, fps)

        # 超低延时优化 - 参考project3配置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))  # MJPEG格式

        # 额外的低延迟优化
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 禁用自动曝光
        cap.set(cv2.CAP_PROP_EXPOSURE, -6)  # 设置固定曝光值
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 禁用自动对焦

        # 设置优先级为性能而非质量
        cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)  # 禁用RGB转换

        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头索引 {index}")

        self.cameras[index] = cap
        return cap

    def release_all(self):
        for cap in self.cameras.values():
            cap.release()
        self.cameras.clear()
